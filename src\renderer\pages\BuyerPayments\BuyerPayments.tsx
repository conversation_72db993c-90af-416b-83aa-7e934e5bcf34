import { useEffect, useRef, useState } from "react";
import { AgGridReact } from "ag-grid-react";
import clsx from "clsx";
import "ag-grid-community/styles/ag-grid.css";
import { useDebouncedValue } from "@mantine/hooks";
import ReactPaginate from "react-paginate";
import { Checkbox, FormControlLabel, MenuItem, Select, TextField } from "@mui/material";
import Loader from "../../components/common/Loader";
import MatPopup from "../../components/common/MatPopup";
import useGetBuyerPayments from "../../hooks/useGetBuyerPayments";
import usePostBuyerPayment from "../../hooks/usePostBuyerPayment";
import styles from "./BuyerPayments.module.scss";
import { ReactComponent as ClearIcon } from '../../../assests/images/Close.svg';

interface BuyerPaymentItem {
  po_number: string;
  total_amt: number;
  amt_received: number;
  balance_amt: number;
  buyer_company: string;
  buyer_name: string;
  seller_company: string;
  seller_name: string;
  comment: string;
  status: string;
}

const BuyerPayments = () => {
  const [rowData, setRowData] = useState<BuyerPaymentItem[]>([]);
  const [inputSearchValue, setInputSearchValue] = useState("");
  const [debouncedInputSearchValue] = useDebouncedValue(inputSearchValue, 1000);
  const [isColDefReady, setIsColDefReady] = useState(false);
  const [perPageEntriesOptions] = useState([10, 25, 50, 100]);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [meta, setMeta] = useState<any>(null);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [apiResponseMessage, setApiResponseMessage] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  
  // Form state
  const [selectedPoNumber, setSelectedPoNumber] = useState("");
  const [amtReceived, setAmtReceived] = useState("");
  const [comment, setComment] = useState("");
  const [cancelPo, setCancelPo] = useState(false);

  const tableRef = useRef(null);

  const {
    data: buyerPaymentsData,
    isLoading: isBuyerPaymentsLoading,
    isFetching: isBuyerPaymentsFetching,
  } = useGetBuyerPayments(itemsPerPage, currentPage, debouncedInputSearchValue);

  const {
    mutate: saveBuyerPayment,
    isLoading: isSaveBuyerPaymentLoading,
  } = usePostBuyerPayment();

  const buyerPayments: BuyerPaymentItem[] = buyerPaymentsData?.items || [];

  // Get selected PO details
  const selectedPoDetails = buyerPayments.find(
    (item) => item.po_number === selectedPoNumber
  );

  useEffect(() => {
    if (isBuyerPaymentsLoading || isBuyerPaymentsFetching) {
      return;
    }

    if (buyerPaymentsData?.meta) {
      setMeta(buyerPaymentsData.meta);
    } else {
      setMeta(null);
    }

    if (buyerPaymentsData?.items?.length) {
      setRowData(buyerPaymentsData.items);
    } else {
      setRowData([]);
    }
  }, [buyerPaymentsData, isBuyerPaymentsFetching, isBuyerPaymentsLoading]);

  useEffect(() => {
    if (selectedPoNumber && selectedPoDetails) {
      setAmtReceived("");
      setComment("");
      setCancelPo(false);
      setErrorMessage("");
    }
  }, [selectedPoNumber, selectedPoDetails]);

  useEffect(() => {
    const balance = calculateBalancePreview();
    if (balance === 0 && selectedPoNumber) {
      setCancelPo(true);
    }
  }, [amtReceived, selectedPoNumber, selectedPoDetails]);

  useEffect(() => {
    updatePinnedData();
  }, [tableRef?.current]);

  const updatePinnedData = () => {
    if (tableRef?.current) {
      if (isColDefReady) {
        return;
      }
      setIsColDefReady(true);
    }
  };

  const formatCurrency = (amount: number | string | null | undefined): string => {
    if (amount === null || amount === undefined || amount === "") return "0.00";
    const num = Number(amount || 0);
    return num.toLocaleString("en-US", {
      style: "currency",
      currency: "USD",
      maximumFractionDigits: 2,
      minimumFractionDigits: 2,
    });
  };

  const getStatusClass = (status: string) => {
    const statusLower = status?.toLowerCase() || "";
    if (statusLower === "paid") {
      return "good";
    } else if (statusLower === "partially paid") {
      return "warn";
    } else if (statusLower === "unpaid") {
      return "bad";
    }
    return "warn";
  };

  const [colDefs] = useState<any[]>([
    {
      field: "add_payment",
      headerName: ``,
      sortable: false,
      minWidth: 130,
      maxWidth: 130,
      pinned: "left" as const,
      cellRenderer: (props: any) => {
        return (
          <button className={styles.editBtn} onClick={() => {
            setShowAddDialog(true);
            setSelectedPoNumber(props?.data?.po_number || "");
            setAmtReceived("");
            setComment("");
            setCancelPo(false);
            setErrorMessage("");
          }}>
            Add Payment
          </button>
        );
      },
    },
    {
      field: "po_number",
      headerName: "PO#",
      minWidth: 140,
      flex: 0.8,
      cellStyle: { fontFamily: "ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas", textAlign: "left" },
    },
    {
      field: "total_amt",
      headerName: "Total Amt",
      minWidth: 150,
      flex: 1,
      cellRenderer: (props: any) => {
        return props?.data?.total_amt ? formatCurrency(props.data.total_amt) : "-";
      },
      cellStyle: { textAlign: "right" },
    },
    {
      field: "amt_received",
      headerName: "Amt Received from Buyer",
      minWidth: 200,
      flex: 1,
      cellRenderer: (props: any) => {
        return props?.data?.amt_received ? formatCurrency(props.data.amt_received) : formatCurrency(0);
      },
      cellStyle: { textAlign: "right" },
    },
    {
      field: "balance_amt",
      headerName: "Balance Amt",
      minWidth: 150,
      flex: 1,
      cellRenderer: (props: any) => {
        return props?.data?.balance_amt ? formatCurrency(props.data.balance_amt) : formatCurrency(0);
      },
      cellStyle: { textAlign: "right" },
    },
    {
      field: "buyer_company",
      headerName: "Buyer Company",
      minWidth: 200,
      flex: 1.2,
    },
    {
      field: "buyer_name",
      headerName: "Buyer Name",
      minWidth: 180,
      flex: 1,
    },
    {
      field: "seller_company",
      headerName: "Seller Company",
      minWidth: 200,
      flex: 1.2,
    },
    {
      field: "seller_name",
      headerName: "Seller Name",
      minWidth: 180,
      flex: 1,
    },
    {
      field: "comment",
      headerName: "Comment",
      minWidth: 250,
      flex: 1.5,
      cellStyle: {
        whiteSpace: 'normal',
        wordWrap: 'break-word',
        lineHeight: '1.4',
        textOverflow: 'unset',
        overflow: 'auto',
        maxHeight: '80px',
      },
      autoHeight: false,
      wrapText: true,
    },
    {
      field: "status",
      headerName: "Status",
      minWidth: 150,
      flex: 0.8,
      cellRenderer: (props: any) => {
        const status = props?.data?.status || "";
        const statusClass = getStatusClass(status);
        const statusClassCapitalized = statusClass.charAt(0).toUpperCase() + statusClass.slice(1);
        return (
          <span className={`${styles.pill} ${styles[`status${statusClassCapitalized}`]}`}>
            {status || "Unknown"}
          </span>
        );
      },
    },
  ]);

  const defaultColDef = {
    sortable: true,
    lockVisible: true,
    unSortIcon: true,
    cellStyle: { flex: 1 },
    headerClass: clsx(styles.header_initial),
    cellClass: styles.cell_default,
    wrapHeaderText: true,
    autoHeaderHeight: true,
    lockPinned: true,
  };

  const onGridReady = (event: any) => {
    event.api.sizeColumnsToFit();
  };

  const gridOptions = {
    icons: {
      sortAscending: '<span class="custom-sort-asc sorted"></span>',
      sortDescending: '<span class="custom-sort-desc sorted"></span>',
      sortUnSort: '<span class="custom-sort-none"></span>',
    },
  };

  const search = (searchValue: string) => {
    setInputSearchValue(searchValue);
    setCurrentPage(1);
  };

  const clearInput = () => {
    setInputSearchValue("");
  };

  const handlePageClick = (event: any) => {
    setCurrentPage(event.selected + 1);
  };

  const closeAddDialog = () => {
    setShowAddDialog(false);
    setSelectedPoNumber("");
    setAmtReceived("");
    setComment("");
    setCancelPo(false);
    setErrorMessage("");
  };

  const handleDialogClose = (event: any, reason: string) => {
    // Only close when explicitly called (e.g., from close button), not from backdrop click or escape key
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) {
      return;
    }
    closeAddDialog();
  };

  const calculateBalancePreview = (): number => {
    if (!selectedPoDetails) return 0;
    const entryAmt = parseFloat(amtReceived) || 0;
    const newReceived = (selectedPoDetails.amt_received || 0) + entryAmt;
    return Math.max(0, (selectedPoDetails.total_amt || 0) - newReceived);
  };

  const isFormValid = () => {
    if (!selectedPoNumber) {
      return false;
    }

    // If balance is 0, allow saving even with empty/0 amount (for closing PO)
    const balance = calculateBalancePreview();
    if (balance === 0) {
      return true;
    }

    // Otherwise, require valid amount received
    const amtReceivedNum = parseFloat(amtReceived);
    if (!amtReceived || isNaN(amtReceivedNum) || amtReceivedNum < 0) {
      return false;
    }

    return true;
  };

  const handleSave = () => {
    if (!isFormValid()) {
      return;
    }

    const amtReceivedNum = parseFloat(amtReceived) || 0;
    const payload = {
      po_number: selectedPoNumber,
      amount_paid: amtReceivedNum,
      close_order: cancelPo,
      ...(comment.trim() && { comment: comment.trim() }),
    };

    saveBuyerPayment(payload, {
      onSuccess: (data: any) => {
        setApiResponseMessage(data?.message || "Buyer payment added successfully");
        closeAddDialog();
      },
      onError: (error: any) => {
        setErrorMessage(error?.message || "Failed to add buyer payment");
      },
    });
  };

  return (
    <div className={styles.buyerPaymentsContainer}>
      {isBuyerPaymentsLoading || isBuyerPaymentsFetching ? (
        <div className={styles.noDataFound}>
          <Loader />
        </div>
      ) : (
        <>
          <div className={styles.searchBox}>
            <div className={styles.sortDataSection}>
              <Select
                className="editLinesDropdown emailAttachDropdown"
                MenuProps={{
                  classes: {
                    paper: styles.Dropdownpaper,
                    list: styles.muiMenuList,
                  },
                }}
                value={itemsPerPage}
                onChange={(event) => {
                  setItemsPerPage(+event.target.value);
                  setCurrentPage(1);
                }}
              >
                {perPageEntriesOptions.map((item, index) => (
                  <MenuItem key={index} value={item}>
                    <span>{item}</span>
                  </MenuItem>
                ))}
              </Select>
            </div>
            <div className={styles.SortRightSection}>
              <div className={styles.searchContainer}>
                <input
                  className={styles.searchInput}
                  type="text"
                  onChange={(e) => search(e.target.value)}
                  placeholder="Search in table (PO#, names, company, comment, amounts…)"
                  value={inputSearchValue}
                />
                {inputSearchValue && (
                  <button className={styles.clearInputIcon} onClick={clearInput}>
                    <ClearIcon />
                  </button>
                )}
              </div>
            </div>
          </div>
          <div
            ref={tableRef}
            className={clsx(styles.ag_theme_quartz, styles.agGridAdmin)}
            style={{
              height: "calc(100vh - 300px)",
              width: "100%",
              minHeight: "400px",
              flex: 1,
              display: "flex",
              flexDirection: "column",
            }}
          >
            {isColDefReady && (
              <AgGridReact
                rowData={rowData}
                columnDefs={colDefs as any}
                sideBar={true}
                suppressCellFocus={true}
                suppressMovableColumns={true}
                rowHeight={80}
                headerHeight={32}
                enableCellTextSelection={true}
                ensureDomOrder={true}
                defaultColDef={defaultColDef}
                embedFullWidthRows={true}
                onGridReady={onGridReady}
                gridOptions={gridOptions}
              />
            )}
          </div>
          {meta && (
            <div className={"PaginationNumber"}>
              <ReactPaginate
                breakLabel="..."
                nextLabel=">"
                onPageChange={handlePageClick}
                pageRangeDisplayed={5}
                pageCount={meta.totalPages}
                previousLabel="<"
                forcePage={meta.currentPage > 0 ? meta.currentPage - 1 : undefined}
                renderOnZeroPageCount={(props) =>
                  props.pageCount > 0 ? undefined : null
                }
              />
            </div>
          )}
        </>
      )}

      {/* Add Buyer Payment Dialog */}
      <MatPopup
        className={styles.orderContinuePopup}
        open={showAddDialog}
        onClose={handleDialogClose}
        maxWidth={false}
        PaperProps={{
          sx: {
            maxWidth: '900px',
            minWidth: '900px',
            width: '90%',
          },
        }}
      >
        <div className={styles.continuePopup}>
          <div className={styles.popupHeader}>
            <p className={styles.continuetext}>Add Buyer Payment</p>
            <button className={styles.closeButton} onClick={closeAddDialog}>
              <ClearIcon />
            </button>
          </div>
          <div className={styles.overFlowForPop}>
            <div className={styles.inputRow}>
              <div className={styles.inputDiv}>
                <label>PO#</label>
                <TextField
                  value={selectedPoNumber || ""}
                  disabled
                  
                  className={styles.textInput}
                />
                <span className={styles.hint}>Select a PO#. The fields below are read-only and come from that PO.</span>
              </div>

              <div className={styles.inputDiv}>
                <label>Total Amt (read-only)</label>
                <TextField
                  value={selectedPoDetails ? formatCurrency(selectedPoDetails.total_amt) : ""}
                  disabled
                  
                  className={styles.textInput}
                />
              </div>
            </div>

            <div className={styles.inputRow}>
              <div className={styles.inputDiv}>
                <label>Buyer Company (read-only)</label>
                <TextField
                  value={selectedPoDetails?.buyer_company || ""}
                  disabled
                  
                  className={styles.textInput}
                />
              </div>

              <div className={styles.inputDiv}>
                <label>Buyer Name (read-only)</label>
                <TextField
                  value={selectedPoDetails?.buyer_name || ""}
                  disabled
                  
                  className={styles.textInput}
                />
              </div>
            </div>

            <div className={styles.inputRow}>
              <div className={styles.inputDiv}>
                <label>Seller Company (read-only)</label>
                <TextField
                  value={selectedPoDetails?.seller_company || ""}
                  disabled
                  
                  className={styles.textInput}
                />
              </div>

              <div className={styles.inputDiv}>
                <label>Seller Name (read-only)</label>
                <TextField
                  value={selectedPoDetails?.seller_name || ""}
                  disabled
                  
                  className={styles.textInput}
                />
              </div>
            </div>

            <div className={styles.inputRow}>
              <div className={styles.inputDiv}>
                <label>Amt Received from Buyer (this entry)</label>
                <TextField
                  type="number"
                  inputProps={{ step: "0.01", min: "0" }}
                  value={amtReceived}
                  onChange={(e) => setAmtReceived(e.target.value)}
                  placeholder="e.g. 25000"
                  
                  className={styles.textInput}
                />
              </div>

              <div className={styles.inputDiv}>
                <label>Balance Amt (computed)</label>
                <TextField
                  value={formatCurrency(calculateBalancePreview())}
                  disabled
                  
                  className={styles.textInput}
                />
              </div>
            </div>

            <div className={styles.inputRow}>
              <div className={styles.inputDiv} style={{ flex: 1 }}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={cancelPo}
                      onChange={(e) => setCancelPo(e.target.checked)}
                      className={styles.cancelPoCheckbox}
                    />
                  }
                  label="Check the box to close the PO#"
                  className={styles.cancelPoLabel}
                  sx={{
                    '& .MuiFormControlLabel-label': {
                      fontSize: '16px',
                      fontWeight: 500,
                      color: '#ff0000',
                      marginLeft: '4px',
                    },
                  }}
                />
              </div>
            </div>

            <div className={styles.inputRow}>
              <div className={styles.inputDiv} style={{ flex: 1 }}>
                <label>Comment</label>
                <TextField
                  multiline
                  rows={4}
                  value={comment}
                  onChange={(e) => setComment(e.target.value)}
                  placeholder="Optional notes (reference ID, bank details, partial payment remark, etc.)"
                  
                  className={styles.textInput}
                />
              </div>
            </div>

            {errorMessage && (
              <div className={styles.errorBox}>
                {errorMessage}
              </div>
            )}
          </div>
          <div className={styles.yesAndnoBtn}>
            <button className={styles.cancelBtn} onClick={closeAddDialog}>
              Cancel
            </button>
            <button
              className={styles.saveBtn}
              onClick={handleSave}
              disabled={isSaveBuyerPaymentLoading || !isFormValid()}
            >
              {isSaveBuyerPaymentLoading ? "Saving..." : "Save"}
            </button>
          </div>
        </div>
      </MatPopup>

      {/* Success Message Popup */}
      <MatPopup
        className={styles.approveRejectPopup}
        open={!!apiResponseMessage}
      >
        <div className={styles.successfullyUpdated}>
          <div className={styles.successfullytext}>{apiResponseMessage}</div>
          <button
            className={styles.okBtn}
            onClick={() => setApiResponseMessage("")}
          >
            Ok
          </button>
        </div>
      </MatPopup>
    </div>
  );
};

export default BuyerPayments;
