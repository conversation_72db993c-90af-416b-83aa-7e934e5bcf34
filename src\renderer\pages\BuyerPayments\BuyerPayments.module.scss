.buyerPaymentsContainer {
  width: 100%;
  height: calc(100vh - 120px);
  max-height: calc(100vh - 120px);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.searchBox {
  margin-bottom: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;

  @media (max-width: 767px) {
    flex-direction: column;
    align-items: flex-start;
  }

  .sortDataSection {
    display: flex;
    align-items: center;
    @media (max-width: 767px) {
      margin-bottom: 12px;
      width: 100%;
    }
  }

  .SortRightSection {
    display: flex;
    align-items: center;
    margin-left: auto;
    @media (max-width: 767px) {
      margin-left: 0;
      width: 100%;
      align-items: flex-start;
      flex-direction: column;
    }
    .searchContainer {
      display: flex;
      position: relative;
      flex: 1;
      
      input {
        padding-right: 30px;
        height: 40px;
        border-radius: 4px;
        border: solid 1px #c4c8d1;
        font-size: 14px;
        padding: 0px 12px;
        width: 100%;
        
        &:focus {
          outline: none;
        }
      }
      
      .clearInputIcon {
        position: absolute;
        top: 8px;
        right: 6px;
        cursor: pointer;
        z-index: 99;
        background-color: transparent;
        border: 0px;
        padding: 0px;
        display: flex;
        align-items: center;
        
        svg {
          width: 24px;
          height: 24px;
          path {
            fill: var(--primaryColor);
          }
        }
      }
      
      @media (max-width: 767px) {
        width: 100%;
      }
    }
  }
}


.noDataFound {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  width: 100%;
  text-align: center;

  .errorMessage {
    font-size: 16px;
    color: #d32f2f;
    text-align: center;
  }
}

.ag_theme_quartz {
  --ag-foreground-color: #676f7c;
  --ag-background-color: white;
  --ag-header-foreground-color: white;
  --ag-header-background-color: #676f7c;
  --ag-odd-row-background-color: #f2f2f2;
  --ag-header-column-resize-handle-color: rgb(126, 46, 132);
  --ag-font-size: 14px;
  --ag-font-family: monospace;
  --ag-icon-font-code-aggregation: "\f247";
  --ag-icon-font-color-group: red;
  --ag-icon-font-weight-group: normal;
  flex: 1;
  min-height: 0;
  overflow: hidden;
}

.agGridAdmin {
  --ag-icon-font-code-asc: '\25B2';
  --ag-icon-font-code-desc: '\25BC';
  --ag-icon-font-code-none: '\25B2\25BC';
  
  .ag-center-cols-viewport {
    min-height: 0;
  }
  
  .ag-icon-asc::before {
    content: var(--ag-icon-font-code-asc);
  }
  
  .ag-icon-none::before {
    content: var(--ag-icon-font-code-none);
    color: green;
    padding: 2px;
    margin-bottom: 5px;
    font-size: 20px;
  }
  
  // Increase specificity to override AG Grid defaults without !important
  .ag-header-cell .ag-icon-none::before {
    font-size: 20px;
  }
  
  .ag-root-wrapper {
    .ag-root-wrapper-body {
      .ag-body-horizontal-scroll-viewport {
        overflow-x: auto;
        
        &::-webkit-scrollbar {
          width: 8px;
          height: 6px;
        }
        
        &::-webkit-scrollbar-track {
          box-shadow: inset 0 0 6px #a8b2bb;
          border-radius: 4px;
        }
        
        &::-webkit-scrollbar-thumb {
          background: #a8b2bb;
          border-radius: 4px;
        }
      }
      
      .ag-header-row {
        .ag-header-cell {
          padding-left: 20px;
          padding-right: 20px;
          line-height: 1.2;
          font-weight: 600;
          font-size: 16px;
          margin: 0;
          text-align: left;
          color: #fff;
          background: #676f7c;
          
          &:hover {
            color: #fff;
            background: #676f7c;
          }
        }
        
        .ag-header-cell:not(.ag-column-resizing) + .ag-header-cell:not(.ag-column-hover):not(.ag-header-cell-moving):hover {
          color: #fff;
          background: #676f7c;
        }
      }
      
      .ag-body-viewport-wrapper.ag-layout-normal {
        overflow-x: scroll;
        overflow-y: scroll;
      }
      
      ::-webkit-scrollbar {
        -webkit-appearance: none;
        width: 8px;
        height: 6px;
      }
      
      ::-webkit-scrollbar-thumb {
        border-radius: 4px;
        background: #a8b2bb;
        box-shadow: inset 0 0 6px #a8b2bb;
      }
      
      .ag-body {
        .ag-body-viewport {
          .ag-center-cols-clipper {
            min-height: 0;
            
            .ag-row-odd {
              background-color: #f2f2f2;
            }
            
            .ag-cell {
              cursor: pointer;

              // Enable text wrapping and scrolling for comment cells
              &[col-id="comment"] {
                white-space: normal !important;
                word-wrap: break-word !important;
                line-height: 1.4 !important;
                text-overflow: unset !important;
                overflow: auto !important;
                max-height: 100px !important;
                padding: 8px 10px !important;

                // Custom scrollbar styling
                &::-webkit-scrollbar {
                  width: 6px;
                }

                &::-webkit-scrollbar-track {
                  background: #f1f1f1;
                  border-radius: 3px;
                }

                &::-webkit-scrollbar-thumb {
                  background: #c1c1c1;
                  border-radius: 3px;

                  &:hover {
                    background: #a8a8a8;
                  }
                }
              }
            }
            
          }
        }
      }
    }
  }
}

.header_initial {
  font-weight: bold;
  font-size: 15px;
  padding-right: 10px;
  padding-left: 10px;
}

.cell_default {
  align-content: center;
  padding-right: 10px;
  padding-left: 10px;
}

.pill {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 6px 8px;
  border-radius: 999px;
  font-size: 12px;
  white-space: nowrap;
  width: 110px;
}

.statusGood {
  border-color: rgba(46, 204, 113, 0.45);
  background: rgba(46, 204, 113, 0.12);
  color: #2ecc71;
}

.statusWarn {
  border-color: rgba(255, 204, 102, 0.45);
  background: rgba(255, 204, 102, 0.12);
  color: #ff9b00;
}

.statusBad {
  border-color: rgba(255, 107, 107, 0.45);
  background: rgba(255, 107, 107, 0.12);
  color: #ff6b6b;
}

.orderContinuePopup {
  h2 {
    display: none;
  }

  // Override global orderContinuePopup Paper styles for wider popup
  .MuiDialog-paper,
  .MuiPaper-root {
    width: 90%;

    @media screen and (max-width: 768px) {
      max-width: 90%;
      min-width: unset;
      width: 90%;
      margin: 15px;
    }
  }

  // Make disabled TextField text darker and more visible
  .MuiTextField-root {
    &.Mui-disabled {
      .MuiInputBase-input {
        color: #333;
        -webkit-text-fill-color: #333;
      }
    }

    .MuiOutlinedInput-root.Mui-disabled {
      .MuiOutlinedInput-input {
        color: #333;
        -webkit-text-fill-color: #333;
      }
    }

    .MuiInputBase-root.Mui-disabled {
      .MuiInputBase-input {
        color: #333;
        -webkit-text-fill-color: #333;
      }
    }
  }

  .continuePopup {
    padding: 24px;
    text-align: left;

    .popupHeader {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid #e0e0e0;
    }

    .continuetext {
      text-align: left;
      font-size: 20px;
      font-weight: 600;
      margin: 0;
      color: var(--primaryColor);
    }

    .closeButton {
      background: transparent;
      border: none;
      cursor: pointer;
      padding: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: opacity 0.2s ease;

      &:hover {
        opacity: 0.7;
      }

      svg {
        width: 24px;
        height: 24px;
        
        path {
          fill: #676f7c;
        }
      }
    }

    .overFlowForPop {
      max-height: 64vh;
      overflow-y: auto;
      text-align: left;
      margin-bottom: 16px;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
        border-radius: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 4px;

        &:hover {
          background: #555;
        }
      }

      .inputRow {
        display: flex;
        column-gap: 20px;
        margin-bottom: 16px;

        @media screen and (max-width: 768px) {
          flex-direction: column;
          column-gap: 0;
        }
      }

      .inputDiv {
        flex: 1;
        display: flex;
        flex-direction: column;

        label {
          display: block;
          margin-bottom: 8px;
          font-size: 14px;
          font-weight: 500;
          color: #4a4a4a;
        }

        .selectInput,
        .textInput {
          width: 100%;

          // Refined TextField design
          .MuiOutlinedInput-root {
            height: 40px;
            background-color: #fff;
            border-radius: 4px;
            transition: all 0.2s ease-in-out;

            // Default border styling
            fieldset {
              border-color: #c4c8d1;
              border-width: 1px;
            }

            // Hover state
            &:hover:not(.Mui-disabled) {
              fieldset {
                border-color: #676f7c;
              }
            }

            // Focus state
            &.Mui-focused:not(.Mui-disabled) {
              fieldset {
                border-color: var(--primaryColor);
                border-width: 1.5px;
              }
            }

            // Disabled state styling
            &.Mui-disabled {
              background-color: #f5f5f5;
              cursor: not-allowed;

              fieldset {
                border-color: #e0e0e0;
              }
            }
          }

          // Input text styling
          .MuiInputBase-input {
            padding: 10px 14px;
            font-size: 14px;
            color: var(--primaryColor);
            line-height: 1.5;

            &::placeholder {
              color: #999;
              opacity: 1;
            }
          }

          // Multiline textarea styling (for Comment field)
          .MuiInputBase-inputMultiline {
            padding: 12px 14px;
            min-height: 100px;
            resize: vertical;
          }
        }

        .hint {
          font-size: 11px;
          color: #8595ad;
          margin-top: 6px;
          line-height: 1.4;
          display: block;
        }

        .cancelPoLabel {
          margin: 0 0 8px 0;
          font-family: Noto Sans;
          display: flex;

          .MuiFormControlLabel-label {
            font-size: 15px;
            font-weight: 500;
            color: #4a4a4a;
            margin-left: 12px;
          }
        }

        .cancelPoCheckbox {
          padding: 0;
          color: var(--primaryColor);

          .MuiSvgIcon-root {
            font-size: 20px;
          }
        }
      }

      .errorBox {
        margin-top: 12px;
        padding: 12px 16px;
        border-radius: 8px;
        border: 1px solid rgba(255, 107, 107, 0.45);
        background: rgba(255, 107, 107, 0.12);
        color: #ff6b6b;
        font-size: 14px;
        line-height: 1.5;
      }
    }

    .yesAndnoBtn {
      display: flex;
      gap: 10px;
      padding-top: 24px;
      border-top: 1px solid #e0e0e0;
      margin-top: 8px;
      justify-content: flex-end;

      .cancelBtn {
        width: 100px;
        height: 45px;
        border-radius: 6px;
        text-decoration: none;
        border: none;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        background-color: #343a40;
        color: #fff;

        &:hover {
          background-color: #23272b;
        }

        &:active {
          background-color: #1d2124;
        }
      }

      .saveBtn {
        width: 100px;
        height: 45px;
        border-radius: 6px;
        text-decoration: none;
        border: none;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        background-color: var(--primaryColor);
        color: #fff;
        opacity: 1;

        &:hover:not(:disabled) {
          opacity: 0.9;
        }

        &:active:not(:disabled) {
          opacity: 0.8;
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }
    }
  }
}

.Dropdownpaper {
  max-height: 300px;
  overflow-y: auto;
}

.muiMenuList {
  padding: 0;
}

.editBtn {
  width: 110px;
  height: 33px;
  border-radius: 4px;
  text-decoration: none;
  border: none;
  font-family: Noto Sans;
  font-size: 14px;
  color: #fff;
  font-weight: 500;
  cursor: pointer;
  background-color: var(--primaryColor);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &:hover {
    opacity: 0.9;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  &:active {
    opacity: 0.8;
    transform: translateY(0);
  }
}

.approveRejectPopup {
  h2 {
    display: none;
  }

  .successfullyUpdated {
    padding: 20px;
    text-align: center;
    width: 300px;

    @media screen and (max-width: 768px) and (min-width: 320px) {
      width: 240px;
    }

    .successfullytext {
      text-align: center;
      font-size: 20px;
      margin-bottom: 24px;
      color: var(--primaryColor);
      @media screen and (max-width: 768px) and (min-width: 320px) {
        font-size: 18px;
      }
    }

    .okBtn {
      width: 100%;
      height: 45px;
      border-radius: 6px;
      text-decoration: none;
      border: none;
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      background-color: var(--primaryColor);
      color: #fff;
      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
  }
}

.inputDiv{
  input{
   height: 40px;
   border-radius: 4px;
   border: 1px solid #c4c8d1;
   font-family: Noto Sans;
   padding: 0px 12px;
   font-size: 14px;
   color: var(--primaryColor);
   line-height: 1.5;
   background-color: #fff;
   &:focus {
    outline: none;
   }
   &[disabled]{
    background-color: #f5f5f5;
    cursor: not-allowed;
    border-color: #e0e0e0;
   }
  }
}